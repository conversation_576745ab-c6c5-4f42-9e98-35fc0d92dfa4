/* BlogSphere - Advanced Blog Platform Styles */

/* CSS Variables */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    --dark-gradient: linear-gradient(135deg, #434343 0%, #000000 100%);
    --light-bg: #f8f9fa;
    --card-shadow: 0 10px 30px rgba(0,0,0,0.1);
    --card-shadow-hover: 0 20px 40px rgba(0,0,0,0.15);
    --border-radius: 20px;
    --transition: all 0.3s ease;
}

/* Base Styles */
body {
    font-family: 'Inter', sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    line-height: 1.6;
}

/* Utility Classes */
.gradient-text {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-shadow {
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.bg-glass {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

.section-padding {
    padding: 100px 0;
}

/* Button Styles */
.btn-gradient-primary {
    background: var(--primary-gradient);
    border: none;
    color: white;
    transition: var(--transition);
    border-radius: 12px;
}

.btn-gradient-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--card-shadow-hover);
    color: white;
}

.btn-gradient-secondary {
    background: var(--secondary-gradient);
    border: none;
    color: white;
    transition: var(--transition);
    border-radius: 12px;
}

.btn-gradient-secondary:hover {
    transform: translateY(-2px);
    box-shadow: var(--card-shadow-hover);
    color: white;
}

.btn-gradient-success {
    background: var(--success-gradient);
    border: none;
    color: white;
    transition: var(--transition);
    border-radius: 12px;
}

.btn-gradient-success:hover {
    transform: translateY(-2px);
    box-shadow: var(--card-shadow-hover);
    color: white;
}

/* Card Styles */
.card-modern {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    transition: var(--transition);
    overflow: hidden;
}

.card-modern:hover {
    transform: translateY(-5px);
    box-shadow: var(--card-shadow-hover);
}

/* Navigation Styles */
.navbar-modern {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 2px 20px rgba(0,0,0,0.1);
    transition: var(--transition);
}

.navbar-modern.scrolled {
    background: rgba(255, 255, 255, 0.98) !important;
    box-shadow: 0 2px 30px rgba(0,0,0,0.15);
}

.nav-link {
    color: #495057 !important;
    transition: var(--transition);
    position: relative;
    font-weight: 500;
}

.nav-link:hover {
    color: #667eea !important;
    transform: translateY(-1px);
}

.nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 50%;
    background: var(--primary-gradient);
    transition: var(--transition);
    transform: translateX(-50%);
}

.nav-link:hover::after {
    width: 80%;
}

.dropdown-menu {
    margin-top: 10px;
    animation: fadeInUp 0.3s ease;
    border: none;
    box-shadow: var(--card-shadow);
    border-radius: 15px;
}

.dropdown-item {
    transition: var(--transition);
    border-radius: 8px;
    margin: 2px 8px;
}

.dropdown-item:hover {
    background: var(--primary-gradient);
    color: white !important;
    transform: translateX(5px);
}

.dropdown-item:hover i {
    color: white !important;
}

/* Hero Section */
.hero-section {
    background: var(--primary-gradient);
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><polygon fill="%23ffffff08" points="0,1000 1000,0 1000,1000"/></svg>');
    background-size: cover;
}

/* Floating Elements */
.floating-element {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
}

.floating-element:nth-child(1) {
    width: 80px;
    height: 80px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.floating-element:nth-child(2) {
    width: 120px;
    height: 120px;
    top: 60%;
    right: 10%;
    animation-delay: 2s;
}

.floating-element:nth-child(3) {
    width: 60px;
    height: 60px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* Category Cards */
.category-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 30px;
    text-align: center;
    transition: var(--transition);
    border: none;
    box-shadow: var(--card-shadow);
}

.category-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--card-shadow-hover);
}

.category-icon {
    width: 80px;
    height: 80px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 2rem;
    color: white;
    transition: var(--transition);
}

.category-card:hover .category-icon {
    transform: scale(1.1);
}

/* Stats Section */
.stats-counter {
    font-size: 3rem;
    font-weight: 800;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Newsletter Section */
.newsletter-section {
    background: var(--primary-gradient);
    border-radius: 30px;
    padding: 60px;
    position: relative;
    overflow: hidden;
}

.newsletter-section::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: rotate 20s linear infinite;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Footer Styles */
.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 45px;
    height: 45px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    text-decoration: none;
    border-radius: 12px;
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

.social-link:hover {
    background: var(--primary-gradient);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.footer-link {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: var(--transition);
    display: flex;
    align-items: center;
}

.footer-link:hover {
    color: white;
    transform: translateX(5px);
}

.footer-link i {
    font-size: 0.8rem;
    opacity: 0.7;
    transition: var(--transition);
}

.footer-link:hover i {
    opacity: 1;
    color: #667eea;
}

/* Newsletter Form */
.newsletter-form .form-control {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    backdrop-filter: blur(10px);
    border-radius: 10px;
}

.newsletter-form .form-control::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.newsletter-form .form-control:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: #667eea;
    color: white;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Scroll to Top Button */
.scroll-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: var(--primary-gradient);
    color: white;
    border: none;
    border-radius: 50%;
    display: none;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    transition: var(--transition);
    z-index: 1000;
}

.scroll-to-top:hover {
    transform: translateY(-3px);
    box-shadow: var(--card-shadow-hover);
}

.scroll-to-top.show {
    display: flex;
}

/* Blog Card Styles */
.blog-card {
    transition: var(--transition);
    border: none;
    overflow: hidden;
}

.blog-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--card-shadow-hover);
}

.card-img-wrapper {
    position: relative;
    overflow: hidden;
}

.card-img-wrapper::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.1) 100%);
    transition: var(--transition);
}

.blog-card:hover .card-img-wrapper::after {
    background: linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.3) 100%);
}

.card-img-top, .card-img-placeholder {
    transition: var(--transition);
}

.blog-card:hover .card-img-top,
.blog-card:hover .card-img-placeholder {
    transform: scale(1.05);
}

.card-overlay {
    background: rgba(0,0,0,0.5);
    transition: var(--transition);
}

.blog-card:hover .card-overlay {
    opacity: 1;
}

.blog-title-link {
    transition: var(--transition);
}

.blog-title-link:hover {
    color: #667eea !important;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* Responsive Design */
@media (max-width: 991px) {
    .navbar-nav {
        margin-top: 20px;
        padding-top: 20px;
        border-top: 1px solid rgba(0,0,0,0.1);
    }
    
    .nav-link {
        padding: 12px 0 !important;
        border-bottom: 1px solid rgba(0,0,0,0.05);
    }
    
    .nav-link:last-child {
        border-bottom: none;
    }
    
    .dropdown-menu {
        border: none;
        box-shadow: none;
        background: rgba(102, 126, 234, 0.05);
        margin: 10px 0;
        border-radius: 10px;
    }
}

@media (max-width: 768px) {
    .hero-section {
        min-height: 80vh;
    }
    
    .section-padding {
        padding: 60px 0;
    }
    
    .stats-counter {
        font-size: 2rem;
    }
    
    .blog-card {
        margin-bottom: 2rem;
    }
    
    .social-link {
        width: 40px;
        height: 40px;
    }
    
    .newsletter-section {
        padding: 40px 20px;
    }
}

@media (max-width: 576px) {
    .category-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .category-card {
        padding: 20px;
    }

    .stats-counter {
        font-size: 1.8rem;
    }
}

/* Authentication Styles */
.auth-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    padding-top: 100px;
}

.auth-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><polygon fill="%23ffffff08" points="0,1000 1000,0 1000,1000"/></svg>');
    background-size: cover;
}

.auth-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    position: relative;
    z-index: 2;
    transition: var(--transition);
}

.auth-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 30px 60px rgba(0,0,0,0.15);
}

.auth-logo {
    width: 60px;
    height: 60px;
    background: var(--primary-gradient);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    transition: var(--transition);
}

.auth-logo:hover {
    transform: scale(1.05);
}

.auth-section .form-control {
    border-radius: 10px;
    border: 1px solid #e0e0e0;
    padding: 12px 15px;
    transition: var(--transition);
    background: rgba(255, 255, 255, 0.9);
}

.auth-section .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    background: white;
}

.auth-section .input-group-text {
    background: rgba(248, 249, 250, 0.9);
    border: 1px solid #e0e0e0;
    transition: var(--transition);
}

.auth-section .form-control:focus + .input-group-text,
.auth-section .input-group-text:has(+ .form-control:focus) {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.1);
}

.btn-auth {
    background: var(--primary-gradient);
    border: none;
    color: white;
    padding: 12px 30px;
    border-radius: 10px;
    font-weight: 600;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.btn-auth::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-auth:hover::before {
    left: 100%;
}

.btn-auth:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
    color: white;
}

.btn-outline-auth {
    border: 2px solid #667eea;
    color: #667eea;
    background: transparent;
    padding: 10px 28px;
    border-radius: 10px;
    font-weight: 600;
    transition: var(--transition);
}

.btn-outline-auth:hover {
    background: #667eea;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

/* Auth Form Animations */
.auth-card .form-group {
    animation: slideInUp 0.6s ease forwards;
    opacity: 0;
    transform: translateY(20px);
}

.auth-card .form-group:nth-child(1) { animation-delay: 0.1s; }
.auth-card .form-group:nth-child(2) { animation-delay: 0.2s; }
.auth-card .form-group:nth-child(3) { animation-delay: 0.3s; }
.auth-card .form-group:nth-child(4) { animation-delay: 0.4s; }

@keyframes slideInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Auth Links */
.auth-section a {
    transition: var(--transition);
}

.auth-section a:hover {
    transform: translateX(3px);
}

/* Alert Styles for Auth */
.auth-section .alert {
    border-radius: 10px;
    border: none;
    animation: fadeInDown 0.5s ease;
}

.auth-section .alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.auth-section .alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Checkbox Styling */
.auth-section .form-check-input {
    border-radius: 4px;
    border: 2px solid #e0e0e0;
    transition: var(--transition);
}

.auth-section .form-check-input:checked {
    background-color: #667eea;
    border-color: #667eea;
}

.auth-section .form-check-input:focus {
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Profile Page Styles */
.profile-avatar {
    width: 120px;
    height: 120px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: white;
    margin: 0 auto;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.profile-avatar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s;
}

.profile-avatar:hover::before {
    transform: translateX(100%);
}

.profile-avatar:hover {
    transform: scale(1.05);
}

/* Dashboard Styles */
.dashboard-stat-card {
    transition: var(--transition);
    cursor: pointer;
}

.dashboard-stat-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--card-shadow-hover);
}

.dashboard-stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.dashboard-stat-card:hover .dashboard-stat-icon {
    transform: scale(1.1) rotate(5deg);
}

/* Responsive Auth Styles */
@media (max-width: 768px) {
    .auth-section {
        padding-top: 80px;
        padding-bottom: 40px;
    }

    .auth-card {
        margin: 20px;
    }

    .auth-logo {
        width: 50px;
        height: 50px;
    }

    .btn-auth {
        padding: 10px 25px;
    }
}

@media (max-width: 576px) {
    .auth-section .container {
        padding-left: 15px;
        padding-right: 15px;
    }

    .auth-card {
        padding: 30px 20px !important;
    }
}
