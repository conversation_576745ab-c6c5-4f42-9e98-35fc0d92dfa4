<!-- Navigation Header -->
<nav class="navbar navbar-expand-lg navbar-light navbar-modern fixed-top">
    <div class="container">
        <!-- Logo -->
        <a class="navbar-brand d-flex align-items-center" href="{{ url('/') }}">
            <div class="d-flex align-items-center">
                <div class="me-2" style="width: 45px; height: 45px; background: var(--primary-gradient); border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                    <i class="fas fa-blog text-white fs-5"></i>
                </div>
                <span class="fw-bold fs-3 gradient-text">BlogSphere</span>
            </div>
        </a>
        
        <!-- Mobile Toggle Button -->
        <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        
        <!-- Navigation Links -->
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav mx-auto">
                <li class="nav-item">
                    <a class="nav-link fw-medium px-3" href="#home">Home</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link fw-medium px-3" href="#featured">Featured</a>
                </li>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle fw-medium px-3" href="#" id="categoriesDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        Categories
                    </a>
                    <ul class="dropdown-menu border-0 shadow-lg" aria-labelledby="categoriesDropdown" style="border-radius: 15px;">
                        <li><a class="dropdown-item py-2" href="#"><i class="fas fa-laptop-code me-2 text-primary"></i>Technology</a></li>
                        <li><a class="dropdown-item py-2" href="#"><i class="fas fa-heart me-2 text-success"></i>Lifestyle</a></li>
                        <li><a class="dropdown-item py-2" href="#"><i class="fas fa-briefcase me-2 text-warning"></i>Business</a></li>
                        <li><a class="dropdown-item py-2" href="#"><i class="fas fa-plane me-2 text-info"></i>Travel</a></li>
                        <li><a class="dropdown-item py-2" href="#"><i class="fas fa-utensils me-2 text-danger"></i>Food</a></li>
                        <li><a class="dropdown-item py-2" href="#"><i class="fas fa-dumbbell me-2 text-success"></i>Health</a></li>
                    </ul>
                </li>
                <li class="nav-item">
                    <a class="nav-link fw-medium px-3" href="#about">About</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link fw-medium px-3" href="#contact">Contact</a>
                </li>
            </ul>
            
            <!-- CTA Buttons -->
            <div class="d-flex align-items-center gap-2">
                <button class="btn btn-outline-primary fw-medium px-3 py-2" style="border-radius: 10px;">
                    <i class="fas fa-sign-in-alt me-1"></i>
                    Sign In
                </button>
                <button class="btn btn-gradient-primary fw-medium px-4 py-2" style="border-radius: 10px;">
                    <i class="fas fa-rocket me-1"></i>
                    Get Started
                </button>
            </div>
        </div>
    </div>
</nav>

<!-- Mobile Search Bar (Hidden by default) -->
<div class="mobile-search d-lg-none" id="mobileSearch" style="display: none;">
    <div class="container py-3">
        <div class="input-group">
            <input type="text" class="form-control" placeholder="Search articles..." style="border-radius: 10px 0 0 10px;">
            <button class="btn btn-gradient-primary" type="button" style="border-radius: 0 10px 10px 0;">
                <i class="fas fa-search"></i>
            </button>
        </div>
    </div>
</div>

<style>
    .navbar-modern {
        transition: all 0.3s ease;
    }
    
    .navbar-modern.scrolled {
        background: rgba(255, 255, 255, 0.98) !important;
        box-shadow: 0 2px 30px rgba(0,0,0,0.15);
    }
    
    .nav-link {
        color: #495057 !important;
        transition: all 0.3s ease;
        position: relative;
    }
    
    .nav-link:hover {
        color: #667eea !important;
        transform: translateY(-1px);
    }
    
    .nav-link::after {
        content: '';
        position: absolute;
        width: 0;
        height: 2px;
        bottom: 0;
        left: 50%;
        background: var(--primary-gradient);
        transition: all 0.3s ease;
        transform: translateX(-50%);
    }
    
    .nav-link:hover::after {
        width: 80%;
    }
    
    .dropdown-menu {
        margin-top: 10px;
        animation: fadeInUp 0.3s ease;
    }
    
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .dropdown-item {
        transition: all 0.3s ease;
        border-radius: 8px;
        margin: 2px 8px;
    }
    
    .dropdown-item:hover {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white !important;
        transform: translateX(5px);
    }
    
    .dropdown-item:hover i {
        color: white !important;
    }
    
    .navbar-toggler {
        padding: 8px 12px;
        border-radius: 8px;
        transition: all 0.3s ease;
    }
    
    .navbar-toggler:hover {
        background: rgba(102, 126, 234, 0.1);
    }
    
    .navbar-toggler:focus {
        box-shadow: none;
    }
    
    @media (max-width: 991px) {
        .navbar-nav {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid rgba(0,0,0,0.1);
        }
        
        .nav-link {
            padding: 12px 0 !important;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        
        .nav-link:last-child {
            border-bottom: none;
        }
        
        .dropdown-menu {
            border: none;
            box-shadow: none;
            background: rgba(102, 126, 234, 0.05);
            margin: 10px 0;
            border-radius: 10px;
        }
        
        .d-flex.align-items-center.gap-2 {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid rgba(0,0,0,0.1);
            justify-content: center;
        }
    }
</style>

<script>
    // Navbar scroll effect
    window.addEventListener('scroll', function() {
        const navbar = document.querySelector('.navbar-modern');
        if (window.scrollY > 50) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    });
    
    // Close mobile menu when clicking on a link
    document.querySelectorAll('.nav-link').forEach(link => {
        link.addEventListener('click', () => {
            const navbarCollapse = document.querySelector('.navbar-collapse');
            if (navbarCollapse.classList.contains('show')) {
                const bsCollapse = new bootstrap.Collapse(navbarCollapse);
                bsCollapse.hide();
            }
        });
    });
</script>
