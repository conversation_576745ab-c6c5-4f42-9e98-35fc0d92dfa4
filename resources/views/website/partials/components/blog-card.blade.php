{{-- Blog Card Component --}}
{{-- Usage: @include('website.partials.components.blog-card', [
    'title' => 'Blog Title',
    'excerpt' => 'Blog excerpt...',
    'category' => 'Technology',
    'categoryColor' => 'primary',
    'author' => '<PERSON>',
    'date' => '2 days ago',
    'image' => 'path/to/image.jpg',
    'link' => '#'
]) --}}

@php
    $categoryColors = [
        'primary' => 'var(--primary-gradient)',
        'success' => 'var(--success-gradient)',
        'warning' => 'var(--warning-gradient)',
        'danger' => 'var(--secondary-gradient)',
        'info' => 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        'secondary' => 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)'
    ];
    
    $categoryGradient = $categoryColors[$categoryColor ?? 'primary'] ?? $categoryColors['primary'];
@endphp

<article class="card card-modern h-100 blog-card" data-aos="fade-up" data-aos-delay="{{ $delay ?? 0 }}">
    <!-- Card Image -->
    <div class="card-img-wrapper position-relative overflow-hidden">
        @if(isset($image) && $image)
            <img src="{{ $image }}" class="card-img-top" alt="{{ $title }}" style="height: 250px; object-fit: cover;">
        @else
            <div class="card-img-placeholder d-flex align-items-center justify-content-center text-white" 
                 style="height: 250px; background: {{ $categoryGradient }};">
                <i class="fas fa-image fs-1 opacity-50"></i>
            </div>
        @endif
        
        <!-- Category Badge -->
        <div class="position-absolute top-0 start-0 m-3">
            <span class="badge text-white px-3 py-2 fw-medium" 
                  style="background: {{ $categoryGradient }}; border-radius: 20px; font-size: 0.85rem;">
                {{ $category ?? 'General' }}
            </span>
        </div>
        
        <!-- Reading Time Badge -->
        @if(isset($readingTime))
        <div class="position-absolute top-0 end-0 m-3">
            <span class="badge bg-dark bg-opacity-75 text-white px-3 py-2" 
                  style="border-radius: 20px; font-size: 0.8rem;">
                <i class="fas fa-clock me-1"></i>
                {{ $readingTime }} min read
            </span>
        </div>
        @endif
        
        <!-- Overlay on Hover -->
        <div class="card-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center opacity-0">
            <a href="{{ $link ?? '#' }}" class="btn btn-light btn-lg rounded-pill px-4">
                <i class="fas fa-arrow-right me-2"></i>
                Read More
            </a>
        </div>
    </div>
    
    <!-- Card Body -->
    <div class="card-body p-4 d-flex flex-column">
        <!-- Title -->
        <h5 class="card-title fw-bold mb-3 flex-grow-0">
            <a href="{{ $link ?? '#' }}" class="text-decoration-none text-dark blog-title-link">
                {{ $title ?? 'Blog Title' }}
            </a>
        </h5>
        
        <!-- Excerpt -->
        <p class="card-text text-muted mb-4 flex-grow-1" style="line-height: 1.6;">
            {{ $excerpt ?? 'This is a sample blog excerpt that gives readers a preview of the content...' }}
        </p>
        
        <!-- Author and Date -->
        <div class="d-flex align-items-center justify-content-between mt-auto">
            <div class="d-flex align-items-center">
                <!-- Author Avatar -->
                <div class="author-avatar me-3">
                    @if(isset($authorImage) && $authorImage)
                        <img src="{{ $authorImage }}" alt="{{ $author ?? 'Author' }}" 
                             class="rounded-circle" style="width: 40px; height: 40px; object-fit: cover;">
                    @else
                        <div class="rounded-circle d-flex align-items-center justify-center text-white fw-bold" 
                             style="width: 40px; height: 40px; background: {{ $categoryGradient }}; font-size: 0.9rem;">
                            {{ substr($author ?? 'A', 0, 1) }}
                        </div>
                    @endif
                </div>
                
                <!-- Author Info -->
                <div>
                    <div class="fw-semibold text-dark" style="font-size: 0.9rem;">
                        {{ $author ?? 'Anonymous' }}
                    </div>
                    <div class="text-muted" style="font-size: 0.8rem;">
                        <i class="fas fa-calendar-alt me-1"></i>
                        {{ $date ?? 'Recently' }}
                    </div>
                </div>
            </div>
            
            <!-- Action Buttons -->
            <div class="d-flex align-items-center gap-2">
                @if(isset($showLike) && $showLike)
                <button class="btn btn-sm btn-outline-danger rounded-pill like-btn" data-bs-toggle="tooltip" title="Like this post">
                    <i class="fas fa-heart"></i>
                    <span class="like-count">{{ $likes ?? 0 }}</span>
                </button>
                @endif
                
                @if(isset($showShare) && $showShare)
                <div class="dropdown">
                    <button class="btn btn-sm btn-outline-primary rounded-pill" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-share-alt"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="#"><i class="fab fa-twitter me-2"></i>Twitter</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fab fa-facebook me-2"></i>Facebook</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fab fa-linkedin me-2"></i>LinkedIn</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-link me-2"></i>Copy Link</a></li>
                    </ul>
                </div>
                @endif
            </div>
        </div>
    </div>
</article>

<style>
    .blog-card {
        transition: all 0.3s ease;
        border: none;
        overflow: hidden;
    }
    
    .blog-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    }
    
    .card-img-wrapper {
        position: relative;
        overflow: hidden;
    }
    
    .card-img-wrapper::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.1) 100%);
        transition: all 0.3s ease;
    }
    
    .blog-card:hover .card-img-wrapper::after {
        background: linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.3) 100%);
    }
    
    .card-img-top, .card-img-placeholder {
        transition: all 0.3s ease;
    }
    
    .blog-card:hover .card-img-top,
    .blog-card:hover .card-img-placeholder {
        transform: scale(1.05);
    }
    
    .card-overlay {
        background: rgba(0,0,0,0.5);
        transition: all 0.3s ease;
    }
    
    .blog-card:hover .card-overlay {
        opacity: 1;
    }
    
    .blog-title-link {
        transition: all 0.3s ease;
    }
    
    .blog-title-link:hover {
        color: #667eea !important;
    }
    
    .author-avatar {
        transition: all 0.3s ease;
    }
    
    .blog-card:hover .author-avatar {
        transform: scale(1.1);
    }
    
    .like-btn {
        transition: all 0.3s ease;
    }
    
    .like-btn:hover {
        background: #dc3545;
        border-color: #dc3545;
        color: white;
    }
    
    .like-btn.liked {
        background: #dc3545;
        border-color: #dc3545;
        color: white;
    }
    
    @media (max-width: 768px) {
        .blog-card {
            margin-bottom: 2rem;
        }
        
        .card-img-wrapper {
            height: 200px;
        }
        
        .card-img-top, .card-img-placeholder {
            height: 200px !important;
        }
    }
</style>

<script>
    // Like button functionality
    document.addEventListener('DOMContentLoaded', function() {
        const likeButtons = document.querySelectorAll('.like-btn');
        
        likeButtons.forEach(button => {
            button.addEventListener('click', function() {
                this.classList.toggle('liked');
                const countSpan = this.querySelector('.like-count');
                let count = parseInt(countSpan.textContent);
                
                if (this.classList.contains('liked')) {
                    count++;
                    this.innerHTML = '<i class="fas fa-heart"></i> <span class="like-count">' + count + '</span>';
                } else {
                    count--;
                    this.innerHTML = '<i class="far fa-heart"></i> <span class="like-count">' + count + '</span>';
                }
            });
        });
    });
</script>
