@extends('website.layout.master')

@section('title', 'Edit Profile - BlogSphere')

@section('content')
<div class="container py-5" style="margin-top: 80px;">
    <!-- Header -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="d-flex align-items-center">
                <a href="{{ route('dashboard') }}" class="btn btn-outline-primary me-3">
                    <i class="fas fa-arrow-left me-1"></i>
                    Back
                </a>
                <div>
                    <h2 class="h3 fw-bold text-dark mb-1">Profile Settings</h2>
                    <p class="text-muted mb-0">Manage your account information and preferences</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row g-4">
        <!-- Profile Information -->
        <div class="col-lg-8">
            <div class="card card-modern">
                <div class="card-header bg-transparent border-0 p-4">
                    <h5 class="fw-bold mb-1">Profile Information</h5>
                    <p class="text-muted mb-0">Update your account's profile information and email address.</p>
                </div>
                <div class="card-body p-4 pt-0">
                    <form method="POST" action="{{ route('profile.update') }}">
                        @csrf
                        @method('patch')

                        <!-- Name -->
                        <div class="mb-3">
                            <label for="name" class="form-label fw-medium">{{ __('Name') }}</label>
                            <input id="name" type="text" class="form-control @error('name') is-invalid @enderror" 
                                   name="name" value="{{ old('name', $user->name) }}" required autofocus autocomplete="name">
                            @error('name')
                                <div class="invalid-feedback">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <!-- Email -->
                        <div class="mb-4">
                            <label for="email" class="form-label fw-medium">{{ __('Email') }}</label>
                            <input id="email" type="email" class="form-control @error('email') is-invalid @enderror" 
                                   name="email" value="{{ old('email', $user->email) }}" required autocomplete="username">
                            @error('email')
                                <div class="invalid-feedback">
                                    {{ $message }}
                                </div>
                            @enderror

                            @if ($user instanceof \Illuminate\Contracts\Auth\MustVerifyEmail && ! $user->hasVerifiedEmail())
                                <div class="alert alert-warning mt-2">
                                    <p class="mb-2">
                                        {{ __('Your email address is unverified.') }}
                                    </p>
                                    <form method="POST" action="{{ route('verification.send') }}">
                                        @csrf
                                        <button type="submit" class="btn btn-sm btn-outline-warning">
                                            {{ __('Click here to re-send the verification email.') }}
                                        </button>
                                    </form>
                                    @if (session('status') === 'verification-link-sent')
                                        <p class="mt-2 text-success small">
                                            {{ __('A new verification link has been sent to your email address.') }}
                                        </p>
                                    @endif
                                </div>
                            @endif
                        </div>

                        <div class="d-flex gap-3">
                            <button type="submit" class="btn btn-gradient-primary">
                                <i class="fas fa-save me-2"></i>
                                {{ __('Save Changes') }}
                            </button>

                            @if (session('status') === 'profile-updated')
                                <div class="alert alert-success mb-0 py-2 px-3" role="alert">
                                    {{ __('Saved.') }}
                                </div>
                            @endif
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Profile Avatar -->
        <div class="col-lg-4">
            <div class="card card-modern">
                <div class="card-header bg-transparent border-0 p-4">
                    <h5 class="fw-bold mb-0">Profile Avatar</h5>
                </div>
                <div class="card-body p-4 pt-0 text-center">
                    <div class="rounded-circle d-flex align-items-center justify-center text-white mx-auto mb-3" 
                         style="width: 120px; height: 120px; background: var(--primary-gradient); font-size: 3rem;">
                        {{ substr(Auth::user()->name, 0, 1) }}
                    </div>
                    <h5 class="fw-bold mb-1">{{ Auth::user()->name }}</h5>
                    <p class="text-muted mb-3">{{ Auth::user()->email }}</p>
                    <button class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-camera me-1"></i>
                        Change Avatar
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Update Password -->
    <div class="row g-4 mt-1">
        <div class="col-lg-8">
            <div class="card card-modern">
                <div class="card-header bg-transparent border-0 p-4">
                    <h5 class="fw-bold mb-1">Update Password</h5>
                    <p class="text-muted mb-0">Ensure your account is using a long, random password to stay secure.</p>
                </div>
                <div class="card-body p-4 pt-0">
                    <form method="POST" action="{{ route('password.update') }}">
                        @csrf
                        @method('put')

                        <!-- Current Password -->
                        <div class="mb-3">
                            <label for="current_password" class="form-label fw-medium">{{ __('Current Password') }}</label>
                            <input id="current_password" type="password" class="form-control @error('current_password', 'updatePassword') is-invalid @enderror" 
                                   name="current_password" autocomplete="current-password">
                            @error('current_password', 'updatePassword')
                                <div class="invalid-feedback">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <!-- New Password -->
                        <div class="mb-3">
                            <label for="password" class="form-label fw-medium">{{ __('New Password') }}</label>
                            <input id="password" type="password" class="form-control @error('password', 'updatePassword') is-invalid @enderror" 
                                   name="password" autocomplete="new-password">
                            @error('password', 'updatePassword')
                                <div class="invalid-feedback">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <!-- Confirm Password -->
                        <div class="mb-4">
                            <label for="password_confirmation" class="form-label fw-medium">{{ __('Confirm Password') }}</label>
                            <input id="password_confirmation" type="password" class="form-control @error('password_confirmation', 'updatePassword') is-invalid @enderror" 
                                   name="password_confirmation" autocomplete="new-password">
                            @error('password_confirmation', 'updatePassword')
                                <div class="invalid-feedback">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <div class="d-flex gap-3">
                            <button type="submit" class="btn btn-gradient-primary">
                                <i class="fas fa-key me-2"></i>
                                {{ __('Update Password') }}
                            </button>

                            @if (session('status') === 'password-updated')
                                <div class="alert alert-success mb-0 py-2 px-3" role="alert">
                                    {{ __('Saved.') }}
                                </div>
                            @endif
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Account -->
    <div class="row g-4 mt-1">
        <div class="col-lg-8">
            <div class="card card-modern border-danger">
                <div class="card-header bg-transparent border-0 p-4">
                    <h5 class="fw-bold mb-1 text-danger">Delete Account</h5>
                    <p class="text-muted mb-0">Once your account is deleted, all of its resources and data will be permanently deleted.</p>
                </div>
                <div class="card-body p-4 pt-0">
                    <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteAccountModal">
                        <i class="fas fa-trash me-2"></i>
                        {{ __('Delete Account') }}
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Account Modal -->
<div class="modal fade" id="deleteAccountModal" tabindex="-1" aria-labelledby="deleteAccountModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header border-0">
                <h5 class="modal-title fw-bold text-danger" id="deleteAccountModalLabel">Delete Account</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p class="text-muted">
                    Are you sure you want to delete your account? Once your account is deleted, all of its resources and data will be permanently deleted. Please enter your password to confirm you would like to permanently delete your account.
                </p>
                <form method="POST" action="{{ route('profile.destroy') }}">
                    @csrf
                    @method('delete')

                    <div class="mb-3">
                        <label for="password_delete" class="form-label fw-medium">{{ __('Password') }}</label>
                        <input id="password_delete" type="password" class="form-control @error('password', 'userDeletion') is-invalid @enderror" 
                               name="password" placeholder="Enter your password">
                        @error('password', 'userDeletion')
                            <div class="invalid-feedback">
                                {{ $message }}
                            </div>
                        @enderror
                    </div>

                    <div class="d-flex gap-3">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-2"></i>
                            {{ __('Delete Account') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection
