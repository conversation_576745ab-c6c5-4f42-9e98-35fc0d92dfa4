<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BlogSphere - Advanced Blog Platform</title>
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="font-inter bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 min-h-screen">
    
    <!-- Navigation Header -->
    <nav class="bg-white/80 backdrop-blur-md border-b border-gray-200/50 sticky top-0 z-50 shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center space-x-2">
                    <div class="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center">
                        <i class="fas fa-blog text-white text-lg"></i>
                    </div>
                    <span class="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                        BlogSphere
                    </span>
                </div>
                
                <!-- Navigation Links -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#home" class="text-gray-700 hover:text-blue-600 font-medium transition-colors duration-200">Home</a>
                    <a href="#featured" class="text-gray-700 hover:text-blue-600 font-medium transition-colors duration-200">Featured</a>
                    <a href="#categories" class="text-gray-700 hover:text-blue-600 font-medium transition-colors duration-200">Categories</a>
                    <a href="#about" class="text-gray-700 hover:text-blue-600 font-medium transition-colors duration-200">About</a>
                    <a href="#contact" class="text-gray-700 hover:text-blue-600 font-medium transition-colors duration-200">Contact</a>
                </div>
                
                <!-- CTA Buttons -->
                <div class="flex items-center space-x-4">
                    <button class="hidden sm:block px-4 py-2 text-gray-700 hover:text-blue-600 font-medium transition-colors duration-200">
                        Sign In
                    </button>
                    <button class="px-6 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-md hover:shadow-lg">
                        Get Started
                    </button>
                    
                    <!-- Mobile Menu Button -->
                    <button class="md:hidden p-2 text-gray-700 hover:text-blue-600">
                        <i class="fas fa-bars text-lg"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="relative py-20 lg:py-32 overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-purple-600/10"></div>
        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h1 class="text-4xl md:text-6xl lg:text-7xl font-bold text-gray-900 mb-6 leading-tight">
                    Welcome to the
                    <span class="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                        Future of Blogging
                    </span>
                </h1>
                <p class="text-xl md:text-2xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
                    Discover amazing stories, connect with passionate writers, and share your own journey with our vibrant community.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
                    <button class="px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl text-lg font-semibold">
                        <i class="fas fa-rocket mr-2"></i>
                        Start Reading
                    </button>
                    <button class="px-8 py-4 bg-white text-gray-700 rounded-xl hover:bg-gray-50 transition-all duration-200 shadow-lg hover:shadow-xl text-lg font-semibold border border-gray-200">
                        <i class="fas fa-play mr-2"></i>
                        Watch Demo
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Floating Elements -->
        <div class="absolute top-20 left-10 w-20 h-20 bg-blue-400/20 rounded-full blur-xl animate-pulse"></div>
        <div class="absolute bottom-20 right-10 w-32 h-32 bg-purple-400/20 rounded-full blur-xl animate-pulse delay-1000"></div>
    </section>

    <!-- Stats Section -->
    <section class="py-16 bg-white/50 backdrop-blur-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
                <div class="text-center">
                    <div class="text-3xl md:text-4xl font-bold text-gray-900 mb-2">10K+</div>
                    <div class="text-gray-600 font-medium">Active Readers</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl md:text-4xl font-bold text-gray-900 mb-2">2.5K+</div>
                    <div class="text-gray-600 font-medium">Published Articles</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl md:text-4xl font-bold text-gray-900 mb-2">500+</div>
                    <div class="text-gray-600 font-medium">Expert Writers</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl md:text-4xl font-bold text-gray-900 mb-2">50+</div>
                    <div class="text-gray-600 font-medium">Categories</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Posts Section -->
    <section id="featured" class="py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    Featured Stories
                </h2>
                <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                    Discover the most engaging and thought-provoking articles from our community
                </p>
            </div>
            
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Featured Post 1 -->
                <article class="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group">
                    <div class="h-48 bg-gradient-to-r from-blue-500 to-purple-600 relative overflow-hidden">
                        <div class="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-all duration-300"></div>
                        <div class="absolute bottom-4 left-4">
                            <span class="px-3 py-1 bg-white/90 text-blue-600 rounded-full text-sm font-semibold">Technology</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors duration-200">
                            The Future of Artificial Intelligence in Web Development
                        </h3>
                        <p class="text-gray-600 mb-4 line-clamp-3">
                            Exploring how AI is revolutionizing the way we build and interact with web applications...
                        </p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full"></div>
                                <div>
                                    <div class="text-sm font-semibold text-gray-900">Alex Johnson</div>
                                    <div class="text-xs text-gray-500">2 days ago</div>
                                </div>
                            </div>
                            <button class="text-blue-600 hover:text-blue-700 font-semibold text-sm">
                                Read More <i class="fas fa-arrow-right ml-1"></i>
                            </button>
                        </div>
                    </div>
                </article>

                <!-- Featured Post 2 -->
                <article class="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group">
                    <div class="h-48 bg-gradient-to-r from-green-500 to-teal-600 relative overflow-hidden">
                        <div class="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-all duration-300"></div>
                        <div class="absolute bottom-4 left-4">
                            <span class="px-3 py-1 bg-white/90 text-green-600 rounded-full text-sm font-semibold">Lifestyle</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-3 group-hover:text-green-600 transition-colors duration-200">
                            Sustainable Living: Small Changes, Big Impact
                        </h3>
                        <p class="text-gray-600 mb-4 line-clamp-3">
                            Discover simple yet effective ways to reduce your environmental footprint and live more sustainably...
                        </p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-gradient-to-r from-green-500 to-teal-600 rounded-full"></div>
                                <div>
                                    <div class="text-sm font-semibold text-gray-900">Sarah Chen</div>
                                    <div class="text-xs text-gray-500">4 days ago</div>
                                </div>
                            </div>
                            <button class="text-green-600 hover:text-green-700 font-semibold text-sm">
                                Read More <i class="fas fa-arrow-right ml-1"></i>
                            </button>
                        </div>
                    </div>
                </article>

                <!-- Featured Post 3 -->
                <article class="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group">
                    <div class="h-48 bg-gradient-to-r from-orange-500 to-red-600 relative overflow-hidden">
                        <div class="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-all duration-300"></div>
                        <div class="absolute bottom-4 left-4">
                            <span class="px-3 py-1 bg-white/90 text-orange-600 rounded-full text-sm font-semibold">Business</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-3 group-hover:text-orange-600 transition-colors duration-200">
                            Building a Successful Startup in 2024
                        </h3>
                        <p class="text-gray-600 mb-4 line-clamp-3">
                            Essential strategies and insights for entrepreneurs looking to launch their next big idea...
                        </p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-gradient-to-r from-orange-500 to-red-600 rounded-full"></div>
                                <div>
                                    <div class="text-sm font-semibold text-gray-900">Mike Rodriguez</div>
                                    <div class="text-xs text-gray-500">1 week ago</div>
                                </div>
                            </div>
                            <button class="text-orange-600 hover:text-orange-700 font-semibold text-sm">
                                Read More <i class="fas fa-arrow-right ml-1"></i>
                            </button>
                        </div>
                    </div>
                </article>
            </div>
        </div>
    </section>

    <!-- Categories Section -->
    <section id="categories" class="py-20 bg-white/50 backdrop-blur-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    Explore Categories
                </h2>
                <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                    Find content that matches your interests and passions
                </p>
            </div>

            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
                <!-- Technology Category -->
                <div class="group cursor-pointer">
                    <div class="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 text-center group-hover:-translate-y-2">
                        <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-laptop-code text-white text-2xl"></i>
                        </div>
                        <h3 class="font-bold text-gray-900 mb-2">Technology</h3>
                        <p class="text-sm text-gray-600">245 articles</p>
                    </div>
                </div>

                <!-- Lifestyle Category -->
                <div class="group cursor-pointer">
                    <div class="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 text-center group-hover:-translate-y-2">
                        <div class="w-16 h-16 bg-gradient-to-r from-green-500 to-teal-600 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-heart text-white text-2xl"></i>
                        </div>
                        <h3 class="font-bold text-gray-900 mb-2">Lifestyle</h3>
                        <p class="text-sm text-gray-600">189 articles</p>
                    </div>
                </div>

                <!-- Business Category -->
                <div class="group cursor-pointer">
                    <div class="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 text-center group-hover:-translate-y-2">
                        <div class="w-16 h-16 bg-gradient-to-r from-orange-500 to-red-600 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-briefcase text-white text-2xl"></i>
                        </div>
                        <h3 class="font-bold text-gray-900 mb-2">Business</h3>
                        <p class="text-sm text-gray-600">156 articles</p>
                    </div>
                </div>

                <!-- Travel Category -->
                <div class="group cursor-pointer">
                    <div class="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 text-center group-hover:-translate-y-2">
                        <div class="w-16 h-16 bg-gradient-to-r from-pink-500 to-rose-600 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-plane text-white text-2xl"></i>
                        </div>
                        <h3 class="font-bold text-gray-900 mb-2">Travel</h3>
                        <p class="text-sm text-gray-600">203 articles</p>
                    </div>
                </div>

                <!-- Food Category -->
                <div class="group cursor-pointer">
                    <div class="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 text-center group-hover:-translate-y-2">
                        <div class="w-16 h-16 bg-gradient-to-r from-yellow-500 to-orange-600 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-utensils text-white text-2xl"></i>
                        </div>
                        <h3 class="font-bold text-gray-900 mb-2">Food</h3>
                        <p class="text-sm text-gray-600">134 articles</p>
                    </div>
                </div>

                <!-- Health Category -->
                <div class="group cursor-pointer">
                    <div class="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 text-center group-hover:-translate-y-2">
                        <div class="w-16 h-16 bg-gradient-to-r from-emerald-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-dumbbell text-white text-2xl"></i>
                        </div>
                        <h3 class="font-bold text-gray-900 mb-2">Health</h3>
                        <p class="text-sm text-gray-600">178 articles</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Newsletter Section -->
    <section class="py-20">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl p-8 md:p-12 text-center text-white relative overflow-hidden">
                <div class="absolute inset-0 bg-black/10"></div>
                <div class="relative z-10">
                    <h2 class="text-3xl md:text-4xl font-bold mb-4">
                        Stay Updated with Our Newsletter
                    </h2>
                    <p class="text-xl mb-8 opacity-90">
                        Get the latest articles and insights delivered straight to your inbox
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                        <input
                            type="email"
                            placeholder="Enter your email address"
                            class="flex-1 px-6 py-4 rounded-xl text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-4 focus:ring-white/30"
                        >
                        <button class="px-8 py-4 bg-white text-blue-600 rounded-xl hover:bg-gray-100 transition-all duration-200 font-semibold shadow-lg">
                            Subscribe
                        </button>
                    </div>
                    <p class="text-sm opacity-75 mt-4">
                        No spam, unsubscribe at any time
                    </p>
                </div>

                <!-- Decorative Elements -->
                <div class="absolute -top-10 -left-10 w-40 h-40 bg-white/10 rounded-full blur-xl"></div>
                <div class="absolute -bottom-10 -right-10 w-40 h-40 bg-white/10 rounded-full blur-xl"></div>
            </div>
        </div>
    </section>
