@extends('website.layout.master')

@section('title', 'Confirm Password - BlogSphere')



@section('content')
<section class="auth-section">
    <!-- Floating Elements -->
    <div class="floating-element"></div>
    <div class="floating-element"></div>
    <div class="floating-element"></div>
    
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="auth-card p-5">
                    <!-- Logo -->
                    <div class="text-center mb-4">
                        <a href="{{ url('/') }}" class="text-decoration-none">
                            <div class="auth-logo">
                                <i class="fas fa-blog text-white fs-3"></i>
                            </div>
                            <h3 class="fw-bold gradient-text mb-0">BlogSphere</h3>
                        </a>
                    </div>
                    
                    <div class="text-center mb-4">
                        <h4 class="fw-bold text-dark">Confirm Password</h4>
                        <p class="text-muted">This is a secure area of the application. Please confirm your password before continuing.</p>
                    </div>

                    <form method="POST" action="{{ route('password.confirm') }}">
                        @csrf

                        <!-- Password -->
                        <div class="mb-4">
                            <label for="password" class="form-label fw-medium">{{ __('Password') }}</label>
                            <div class="input-group">
                                <span class="input-group-text bg-light border-end-0">
                                    <i class="fas fa-lock text-muted"></i>
                                </span>
                                <input id="password" type="password" class="form-control border-start-0 @error('password') is-invalid @enderror" 
                                       name="password" required autocomplete="current-password" 
                                       placeholder="Enter your password">
                            </div>
                            @error('password')
                                <div class="invalid-feedback d-block">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <!-- Submit Button -->
                        <div class="d-grid mb-4">
                            <button type="submit" class="btn btn-auth">
                                <i class="fas fa-check me-2"></i>
                                {{ __('Confirm') }}
                            </button>
                        </div>
                    </form>
                    
                    <!-- Back to Home -->
                    <div class="text-center mt-4">
                        <a href="{{ url('/') }}" class="text-muted text-decoration-none">
                            <i class="fas fa-home me-1"></i>
                            Back to Home
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
