<x-app-layout>
    <x-slot name="header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2 class="h3 fw-bold text-dark mb-1">Dashboard</h2>
                <p class="text-muted mb-0">Welcome back, {{ Auth::user()->name }}!</p>
            </div>
            <div>
                <span class="badge bg-success px-3 py-2">
                    <i class="fas fa-circle me-1" style="font-size: 0.6rem;"></i>
                    Online
                </span>
            </div>
        </div>
    </x-slot>

    <div class="container">
        <!-- Stats Cards -->
        <div class="row g-4 mb-5">
            <div class="col-lg-3 col-md-6">
                <div class="card card-modern h-100">
                    <div class="card-body p-4">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="rounded-circle d-flex align-items-center justify-center"
                                     style="width: 50px; height: 50px; background: var(--primary-gradient);">
                                    <i class="fas fa-file-alt text-white"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="text-muted mb-1">Total Posts</h6>
                                <h3 class="fw-bold mb-0">12</h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="card card-modern h-100">
                    <div class="card-body p-4">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="rounded-circle d-flex align-items-center justify-center"
                                     style="width: 50px; height: 50px; background: var(--success-gradient);">
                                    <i class="fas fa-eye text-white"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="text-muted mb-1">Total Views</h6>
                                <h3 class="fw-bold mb-0">1,234</h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="card card-modern h-100">
                    <div class="card-body p-4">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="rounded-circle d-flex align-items-center justify-center"
                                     style="width: 50px; height: 50px; background: var(--warning-gradient);">
                                    <i class="fas fa-heart text-white"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="text-muted mb-1">Total Likes</h6>
                                <h3 class="fw-bold mb-0">89</h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="card card-modern h-100">
                    <div class="card-body p-4">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="rounded-circle d-flex align-items-center justify-center"
                                     style="width: 50px; height: 50px; background: var(--secondary-gradient);">
                                    <i class="fas fa-comments text-white"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="text-muted mb-1">Comments</h6>
                                <h3 class="fw-bold mb-0">45</h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row g-4 mb-5">
            <div class="col-lg-8">
                <div class="card card-modern">
                    <div class="card-header bg-transparent border-0 p-4">
                        <h5 class="fw-bold mb-0">Quick Actions</h5>
                    </div>
                    <div class="card-body p-4 pt-0">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <a href="#" class="btn btn-gradient-primary w-100 py-3">
                                    <i class="fas fa-plus me-2"></i>
                                    Create New Post
                                </a>
                            </div>
                            <div class="col-md-6">
                                <a href="#" class="btn btn-outline-primary w-100 py-3">
                                    <i class="fas fa-edit me-2"></i>
                                    Manage Posts
                                </a>
                            </div>
                            <div class="col-md-6">
                                <a href="#" class="btn btn-outline-success w-100 py-3">
                                    <i class="fas fa-chart-line me-2"></i>
                                    View Analytics
                                </a>
                            </div>
                            <div class="col-md-6">
                                <a href="{{ route('profile.edit') }}" class="btn btn-outline-warning w-100 py-3">
                                    <i class="fas fa-user-cog me-2"></i>
                                    Edit Profile
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="card card-modern">
                    <div class="card-header bg-transparent border-0 p-4">
                        <h5 class="fw-bold mb-0">Profile Summary</h5>
                    </div>
                    <div class="card-body p-4 pt-0 text-center">
                        <div class="rounded-circle d-flex align-items-center justify-center text-white mx-auto mb-3"
                             style="width: 80px; height: 80px; background: var(--primary-gradient); font-size: 2rem;">
                            {{ substr(Auth::user()->name, 0, 1) }}
                        </div>
                        <h5 class="fw-bold mb-1">{{ Auth::user()->name }}</h5>
                        <p class="text-muted mb-3">{{ Auth::user()->email }}</p>
                        <span class="badge bg-primary px-3 py-2">Author</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="card card-modern">
            <div class="card-header bg-transparent border-0 p-4">
                <h5 class="fw-bold mb-0">Recent Activity</h5>
            </div>
            <div class="card-body p-4 pt-0">
                <div class="text-center py-5">
                    <i class="fas fa-clipboard-list display-1 text-muted mb-3"></i>
                    <h5 class="text-muted">No recent activity</h5>
                    <p class="text-muted">Start creating content to see your activity here.</p>
                    <a href="#" class="btn btn-gradient-primary">
                        <i class="fas fa-plus me-2"></i>
                        Create Your First Post
                    </a>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
